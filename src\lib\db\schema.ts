import { pgTable, text, timestamp, integer, decimal, boolean, json } from 'drizzle-orm/pg-core';
import { createId } from '@paralleldrive/cuid2';

// Users table for authentication
export const users = pgTable('users', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  name: text('name'),
  email: text('email').notNull().unique(),
  emailVerified: timestamp('emailVerified', { mode: 'date' }),
  image: text('image'),
  role: text('role', { enum: ['admin', 'test_checker'] }).notNull().default('test_checker'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Accounts table for OAuth
export const accounts = pgTable('accounts', {
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
  type: text('type').notNull(),
  provider: text('provider').notNull(),
  providerAccountId: text('providerAccountId').notNull(),
  refresh_token: text('refresh_token'),
  access_token: text('access_token'),
  expires_at: integer('expires_at'),
  token_type: text('token_type'),
  scope: text('scope'),
  id_token: text('id_token'),
  session_state: text('session_state'),
});

// Sessions table for authentication
export const sessions = pgTable('sessions', {
  sessionToken: text('sessionToken').primaryKey(),
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
  expires: timestamp('expires', { mode: 'date' }).notNull(),
});

// Verification tokens
export const verificationTokens = pgTable('verificationTokens', {
  identifier: text('identifier').notNull(),
  token: text('token').notNull(),
  expires: timestamp('expires', { mode: 'date' }).notNull(),
});

// Candidates table
export const candidates = pgTable('candidates', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  fullName: text('full_name').notNull(),
  email: text('email').notNull().unique(),
  phoneNumber: text('phone_number').notNull(),
  dateOfBirth: timestamp('date_of_birth', { mode: 'date' }).notNull(),
  nationality: text('nationality').notNull(),
  passportNumber: text('passport_number').notNull().unique(),
  testDate: timestamp('test_date', { mode: 'date' }).notNull(),
  testCenter: text('test_center').notNull(),
  photoUrl: text('photo_url'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Test results table
export const testResults = pgTable('test_results', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  candidateId: text('candidate_id').notNull().references(() => candidates.id, { onDelete: 'cascade' }),

  // Listening scores
  listeningScore: decimal('listening_score', { precision: 3, scale: 1 }),
  listeningBandScore: decimal('listening_band_score', { precision: 2, scale: 1 }),

  // Reading scores
  readingScore: decimal('reading_score', { precision: 3, scale: 1 }),
  readingBandScore: decimal('reading_band_score', { precision: 2, scale: 1 }),

  // Writing scores
  writingTask1Score: decimal('writing_task1_score', { precision: 2, scale: 1 }),
  writingTask2Score: decimal('writing_task2_score', { precision: 2, scale: 1 }),
  writingBandScore: decimal('writing_band_score', { precision: 2, scale: 1 }),

  // Speaking scores
  speakingFluencyScore: decimal('speaking_fluency_score', { precision: 2, scale: 1 }),
  speakingLexicalScore: decimal('speaking_lexical_score', { precision: 2, scale: 1 }),
  speakingGrammarScore: decimal('speaking_grammar_score', { precision: 2, scale: 1 }),
  speakingPronunciationScore: decimal('speaking_pronunciation_score', { precision: 2, scale: 1 }),
  speakingBandScore: decimal('speaking_band_score', { precision: 2, scale: 1 }),

  // Overall score
  overallBandScore: decimal('overall_band_score', { precision: 2, scale: 1 }),

  // Status and metadata
  status: text('status', { enum: ['pending', 'completed', 'verified'] }).notNull().default('pending'),
  enteredBy: text('entered_by').references(() => users.id),
  verifiedBy: text('verified_by').references(() => users.id),
  certificateGenerated: boolean('certificate_generated').default(false),
  certificateUrl: text('certificate_url'),

  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// AI feedback table
export const aiFeedback = pgTable('ai_feedback', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  testResultId: text('test_result_id').notNull().references(() => testResults.id, { onDelete: 'cascade' }),

  // Feedback for each skill
  listeningFeedback: text('listening_feedback'),
  readingFeedback: text('reading_feedback'),
  writingFeedback: text('writing_feedback'),
  speakingFeedback: text('speaking_feedback'),

  // Overall feedback and recommendations
  overallFeedback: text('overall_feedback'),
  recommendations: text('recommendations'),

  // Strengths and weaknesses
  strengths: json('strengths').$type<string[]>(),
  weaknesses: json('weaknesses').$type<string[]>(),

  // Study plan suggestions
  studyPlan: json('study_plan').$type<{
    timeframe: string;
    focusAreas: string[];
    resources: string[];
    practiceActivities: string[];
  }>(),

  generatedAt: timestamp('generated_at').defaultNow().notNull(),
});

// Export types
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type Candidate = typeof candidates.$inferSelect;
export type NewCandidate = typeof candidates.$inferInsert;
export type TestResult = typeof testResults.$inferSelect;
export type NewTestResult = typeof testResults.$inferInsert;
export type AIFeedback = typeof aiFeedback.$inferSelect;
export type NewAIFeedback = typeof aiFeedback.$inferInsert;
